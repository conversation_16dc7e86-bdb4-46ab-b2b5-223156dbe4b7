# TIGRAMITE 中文教程文件夹内容整理

## 概述

`tutorials_CN` 文件夹包含了 TIGRAMITE 时间序列因果推断库的中文教程集合。TIGRAMITE 是一个用于从时间序列数据中重建图形模型（条件独立图）的 Python 模块，基于 PCMCI 框架。

## 根目录文件

### Runge_Causal_Inference_for_Time_Series_NREE.pdf
- **类型**: PDF 文档
- **内容**: Nature Review Earth and Environment 期刊论文，提供时间序列因果推断的综合概述
- **作用**: 理论背景和方法选择指南

## 子文件夹结构及内容

### 1. benchmarking_and_validation (基准测试与验证)

**主要内容**: 模型验证和基准测试方法

#### 核心文件:
- `tigramite_tutorial_explaining_correlations.ipynb`
  - **主题**: 使用假设或发现的因果模型解释相关性
  - **内容**: 
    - 估计因果图的马尔可夫等价类
    - 拟合线性结构因果模型
    - 构造线性高斯结构因果模型
    - 比较原始数据与生成数据的滞后相关性
  - **应用**: 验证重建的因果网络是否合理

### 2. case_studies (案例研究)

**主要内容**: 实际应用案例，展示在不同领域的应用

#### 核心文件:
- `climate_case_study.ipynb`
  - **主题**: 气候科学案例研究 - Walker 环流分析
  - **内容**:
    - 分析赤道西-中太平洋 Walker 环流分支
    - 研究 La Nina 和 El Nino 阶段的环流强度变化
    - 季节性政权依赖性分析
    - 冬季中性或 La Nina 阶段的异常环流因果理解
  - **方法**: 遵循 QAD 问卷和方法选择流程图

- `biogeoscience_case_study.ipynb`
  - **主题**: 生物地球科学案例研究
  - **内容**: 生物地球科学领域的因果推断应用

- `climate_data/` 子文件夹
  - **内容**: 气候案例研究所需的数据文件

### 3. causal_discovery (因果发现)

**主要内容**: 因果发现方法和算法的详细教程

#### 核心文件:
- `tigramite_tutorial_causal_discovery_overview.ipynb`
  - **主题**: TIGRAMITE 因果发现概述
  - **内容**:
    - 基本使用方法
    - 绘图功能
    - 整合专家假设
    - 基准测试和验证
    - 因果效应估计
    - 数据集挑战
    - 滑动窗口分析

- `tigramite_tutorial_pcmciplus.ipynb`
  - **主题**: PCMCI+ 算法教程
  - **内容**: PCMCI 算法的增强版本

- `tigramite_tutorial_latent-pcmci.ipynb`
  - **主题**: 潜在变量 PCMCI 方法
  - **内容**: 处理隐藏变量的因果发现

- `tigramite_tutorial_jpcmciplus.ipynb`
  - **主题**: JPCMCI+ 算法
  - **内容**: 联合因果发现方法

- `tigramite_tutorial_conditional_independence_tests.ipynb`
  - **主题**: 条件独立性测试
  - **内容**: 各种条件独立性测试方法的使用

- `tigramite_tutorial_assumptions.ipynb`
  - **主题**: 因果发现中的假设
  - **内容**: 理论假设和实际估计之间的关系

- `tigramite_tutorial_bootstrap_aggregation.ipynb`
  - **主题**: 自举聚合方法
  - **内容**: 提高因果发现稳定性的方法

- `tigramite_tutorial_heteroskedastic_ParCorrWLS.ipynb`
  - **主题**: 异方差偏相关加权最小二乘法
  - **内容**: 处理异方差数据的方法

- `tigramite_tutorial_pairwise_mult_ci.ipynb`
  - **主题**: 成对多重条件独立性
  - **内容**: 成对变量间的条件独立性分析

- `tigramite_tutorial_pcmci_fullci.ipynb`
  - **主题**: PCMCI 完整条件独立性
  - **内容**: 完整的条件独立性测试框架

- `tigramite_tutorial_regime_pcmci.ipynb`
  - **主题**: 政权 PCMCI
  - **内容**: 处理不同政权或状态的因果发现

- `tigramite_tutorial_sliding_window_analysis.ipynb`
  - **主题**: 滑动窗口分析
  - **内容**: 时变因果关系的分析方法

#### figures/ 子文件夹
包含教程中使用的图表和示意图，如：
- 因果平稳性图表
- 时间序列 DAG、DMAG、DPAG 图
- 协同效应图
- 各种算法流程图

### 4. causal_effect_estimation (因果效应估计)

**主要内容**: 因果效应的估计和中介分析

#### 核心文件:
- `tigramite_tutorial_general_causal_effect_analysis.ipynb`
  - **主题**: 一般因果效应分析
  - **内容**:
    - 线性和非线性条件因果效应估计
    - 最优调整集的中介分析
    - 处理隐藏变量的因果图模型
  - **理论基础**: 最优调整理论

- `tigramite_tutorial_general_causal_effect_mediation.ipynb`
  - **主题**: 一般因果效应中介分析
  - **内容**: 中介效应的识别和估计

- `tigramite_tutorial_linear_causal_effects_mediation.ipynb`
  - **主题**: 线性因果效应中介
  - **内容**: 线性模型中的中介分析

#### figures/ 子文件夹
包含因果效应分析相关的图表

### 5. causal_feature_learning_and_prediction (因果特征学习与预测)

**主要内容**: 基于因果关系的特征学习和预测方法

#### 核心文件:
- `tigramite_tutorial_prediction.ipynb`
  - **主题**: 因果预测
  - **内容**:
    - 使用 PCMCI 获得最优预测器
    - 多变量时间序列的无模型预测
    - 因果特征选择在预测中的应用
  - **理论基础**: 最优无模型预测理论

### 6. dataset_challenges (数据集挑战)

**主要内容**: 处理实际数据中常见问题的方法

#### 核心文件:
- `tigramite_tutorial_missing_masking.ipynb`
  - **主题**: 缺失值和掩码处理
  - **内容**:
    - 缺失值的处理策略
    - 数据掩码技术
    - 不完整数据的因果发现

- `tigramite_tutorial_multiple_datasets.ipynb`
  - **主题**: 多数据集处理
  - **内容**: 
    - 多个数据集的联合分析
    - 跨数据集的因果关系验证

#### figures/ 子文件夹
包含数据处理相关的示例图表

## 教程特点

1. **理论与实践结合**: 每个教程都包含理论背景和实际代码示例
2. **循序渐进**: 从基础概念到高级应用的完整学习路径
3. **实际应用导向**: 包含气候科学和生物地球科学的真实案例
4. **方法全面**: 涵盖因果发现、效应估计、预测等多个方面
5. **问题解决**: 专门处理实际数据分析中的常见挑战

## 使用建议

1. **初学者**: 从 `causal_discovery_overview` 开始
2. **特定应用**: 根据需求选择相应的子文件夹
3. **实际项目**: 参考 `case_studies` 中的实际应用案例
4. **数据问题**: 查看 `dataset_challenges` 中的解决方案

这个教程集合为时间序列因果推断提供了全面的学习资源，适合研究人员、数据科学家和相关领域的从业者使用。
