{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 气候案例研究\n", "\n", "本文件夹中来自气候科学和生物地球科学的两个案例研究遵循以下综述论文中的QAD问卷和方法选择流程图（包含在tigramite github教程文件夹中）：\n", "\n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, V. & <PERSON>-<PERSON>, <PERSON><PERSON> inference for time series. Nat. Rev. Earth Environ. 10, 2553 (2023).\n", "\n", "该综述论文末尾列出了解决特定QAD问题的方法和软件清单。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["本案例研究以沃克环流为例，其因果物理机制在所谓的气候遥相关方面已被相当充分地理解。详细讨论请参见论文。\n", "\n", "研究重点是沃克环流的赤道西-中太平洋分支。该环流分支描述了一个顺时针的大尺度时间平均环流，包括中太平洋的下沉气团（CPAC）、西-中太平洋的西向表面信风（WCPAC）和西太平洋的上升气团（WPAC），如[此示意图](https://www.climate.gov/news-features/blogs/enso/walker-circulation-ensos-atmospheric-buddy)所示。已知该环流表现出状态依赖性，即在拉尼娜阶段（定义为中东太平洋异常冷的海洋温度）期间增强，在厄尔尼诺阶段（定义为异常暖的海洋温度）期间减弱（甚至逆转并向东移动）。此外，该环流还表现出季节性状态依赖性，环流强度在冬季达到峰值。下面的分析旨在获得对冬季中性或拉尼娜阶段异常环流的因果理解。\n", "\n", "让我们首先导入一些标准的Python包以及tigramite因果推断包。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import sys\n", "\n", "import sklearn\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "import tigramite\n", "import tigramite.data_processing as pp\n", "import tigramite.plotting as tp\n", "\n", "from tigramite.models import LinearMediation, Models\n", "from tigramite.causal_effects import CausalEffects\n", "\n", "# 去除季节平均值并除以季节标准差的函数\n", "def anomalize(dataseries, divide_by_std=True, reference_bounds = None, cycle_length=12, return_cycle=False):\n", "    if reference_bounds is None:\n", "        reference_bounds = (0, len(dataseries))\n", "\n", "    anomaly = np.copy(dataseries)\n", "    for t in range(cycle_length):\n", "        if return_cycle:\n", "            anomaly[t::cycle_length] = dataseries[t+reference_bounds[0]:reference_bounds[1]:cycle_length].mean(axis=0)\n", "        else:\n", "            anomaly[t::cycle_length] -= dataseries[t+reference_bounds[0]:reference_bounds[1]:cycle_length].mean(axis=0)\n", "            if divide_by_std:\n", "                anomaly[t::cycle_length] /= dataseries[t+reference_bounds[0]:reference_bounds[1]:cycle_length].std(axis=0)\n", "    return anomaly"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据加载和预处理\n", "\n", "主要研究问题是量化相关过程之间的因果效应。严格遵循QAD问卷（见论文），第一步是定义和构建感兴趣的变量。这里的基础ERA5再分析数据以经纬度网格形式提供，需要采用降维方法。如论文案例研究图(a)面板所示，选择WPAC和CPAC指数作为变量，因为它们分别对应于上升和下降气团的主要区域，这通过它们与Nino3.4指数的相关性来衡量。WCPAC指数测量两个区域之间的表面纬向风，被选为第三个变量。\n", "\n", "`/climate_data/`文件夹中的三个变量是使用KNMI气候探索器(https://climexp.knmi.nl/)生成的，详细信息请参见文件头部。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 选择时间范围\n", "start_year = 1950\n", "length = 12*(2022 - start_year)\n", "\n", "# 加载三个气候时间序列\n", "# （这些数据是用KNMI气候探索器生成的，详细信息请参见文件头部）\n", "data = np.vstack((\n", "    np.loadtxt('climate_data/iera5_w700_130-150E_20-0N_n.dat', skiprows=20)[:, 1:].reshape(-1)[:length],\n", "    np.loadtxt('climate_data/iera5_u10m_160-180E_5--5N_n.dat', skiprows=20)[:, 1:].reshape(-1)[:length],\n", "    np.loadtxt('climate_data/iera5_w700_-160--120E_5--5N_n.dat', skiprows=20)[:, 1:].reshape(-1)[:length],\n", "                )).T\n", "T, N = data.shape\n", "\n", "# 定义时间轴的简单方法\n", "datatime = np.linspace(start_year, 2023-1./12., T)\n", "\n", "# 全程使用的变量名\n", "var_names = ['WPAC', 'WCPAC', 'CPAC']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这三个变量都是连续值变量，作为下一步，还将在时间上聚合为2个月的时间分辨率，以平均掉噪声的月度变异性。此过程定义了一个包含三个双月时间序列的单一观测数据集。 "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 对数据和时间进行时间分箱\n", "data, _ = pp.time_bin_with_mask(data,\n", "    time_bin_length=2, mask=None)\n", "datatime, _ = pp.time_bin_with_mask(datatime,\n", "    time_bin_length=2, mask=None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下一个问题（见QAD流程图）是关于平稳性。趋势和季节周期可能引入无法用因果关系解释的非平稳依赖关系，即可能充当混杂因子。因此，作为预处理步骤，通过首先减去趋势（假设由例如温室气体强迫引起）和季节周期，然后除以季节方差来去除趋势和季节周期。用于寻找长期趋势的高斯核的长度尺度假设为十年（$15$年）。 "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 平滑宽度设置为15年\n", "cycle_length = 6   # 双月时间分辨率下的一年\n", "smooth_width = 15*cycle_length\n", "\n", "if smooth_width is not None:\n", "    smoothdata_here = pp.smooth(np.copy(data), smooth_width=smooth_width, kernel='gaussian',\n", "                    residuals=False)\n", "    data_here = pp.smooth(np.copy(data), smooth_width=smooth_width, kernel='gaussian',\n", "                    residuals=True)\n", "else:\n", "    print(\"未平滑。\")\n", "    data_here = np.copy(data)\n", "\n", "# 去除季节平均值并除以季节标准差\n", "seasonal_cycle = anomalize(np.copy(data_here), cycle_length=cycle_length, return_cycle=True)\n", "smoothdata_here += seasonal_cycle"]}, {"cell_type": "markdown", "metadata": {}, "source": ["此外，考虑到上述系统的状态行为，仅考虑中性或拉尼娜阶段（定义为Nino3.4指数的5个月滑动平均值低于0.5$^\\circ$K）期间的11月至2月时期。在技术层面上，这种考虑时间段的选择是通过应用所谓的掩码来实现的（另请参见tigramite中的[掩码教程](https://github.com/jakobrunge/tigramite/blob/master/tutorials/dataset_challenges/tigramite_tutorial_missing_masking.ipynb)）。\n", "\n", "更具体地说，这里的掩码使得时间$t$的WPAC、WCPAC和CPAC样本只能来自中性或拉尼娜条件下的11月至2月，而时间$t-1$和$t-2$的样本也可以来自此掩码之外。这种掩码程序在1950-2021年期间产生了$n=110$的小样本量（厄尔尼诺阶段的样本更少），但至少这些样本可以合理地假设来自同一平稳分布。 "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# 海洋尼诺指数定义为Nino3.4海表温度3个月滑动平均值连续5个月高于/低于0.5度\n", "nino34 = np.loadtxt('climate_data/iersst_nino3.4a.txt', skiprows=44)[(start_year-1854)*12:(start_year-1854)*12+length,1]\n", "nino34smoothed = pp.smooth(nino34, smooth_width=3, kernel='heaviside',\n", "           mask=None, residuals=False)\n", "\n", "# 构建仅用于中性和拉尼娜阶段的掩码\n", "nino_mask = np.zeros(length)\n", "for t in range(length):\n", "    if np.sum(nino34smoothed[max(0, t-4): min(length, t+5)] > 0.5) >= 5:\n", "        nino_mask[t] = 1\n", "\n", "# 对掩码进行时间分箱，因为我们将使用双月时间序列\n", "nino_mask, _ = pp.time_bin_with_mask(nino_mask,\n", "    time_bin_length=2, mask=None)\n", "\n", "# 构建掩码以仅选择11月至2月\n", "# （cycle_length是双月时间序列中一年的长度）\n", "cycle_length = 6\n", "mask = np.ones(data.shape, dtype='bool')\n", "for i in [0, 5]:\n", "    mask[i::cycle_length, :] = False\n", "\n", "# 额外掩码以仅选择中性和拉尼娜阶段\n", "for t in range(mask.shape[0]):\n", "    if nino_mask[t] >= 0.5:\n", "        mask[t] = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下图显示了（黑色）原始数据和叠加的（红色）1）包括长期趋势的低频变异性（通过使用15年长度尺度的高斯核平滑时间序列获得）和2）去趋势残差的季节周期的总和。掩码样本为灰色。"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************************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***********************************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", "text/plain": ["<Figure size 800x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Dataframe for raw data\n", "raw_dataframe = pp.DataFrame(np.copy(data), mask=mask, var_names=var_names, datatime=datatime)\n", "\n", "# Dataframe for smoothed data\n", "smoothdataframe_here = pp.DataFrame(smoothdata_here, var_names=var_names,  datatime=datatime)\n", "\n", "fig, axes = tp.plot_timeseries(\n", "        raw_dataframe,\n", "        figsize=(8, 5),\n", "        grey_masked_samples='data',\n", "        color='black',\n", "        show_meanline=True,\n", "        adjust_plot=False,\n", "        )  \n", "\n", "tp.plot_timeseries(\n", "        smoothdataframe_here,\n", "        fig_axes = (fig, axes),\n", "        grey_masked_samples='data',\n", "        show_meanline=False,\n", "        color='red',\n", "        alpha=0.4,\n", "        adjust_plot=True,\n", "        tick_label_size=7,\n", "        label_fontsize=8,\n", "        time_label='year',\n", "        var_units=['Pa/s', 'm/s', 'Pa/s',],\n", "#         save_name=\"timeseries.pdf\"\n", "        ); plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们继续使用预处理后的数据。"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["if smooth_width is not None:\n", "    data_here = pp.smooth(data=np.copy(data), smooth_width=smooth_width, kernel='gaussian',\n", "                    residuals=True)\n", "else:\n", "    data_here = np.copy(data)\n", "\n", "data_here = anomalize(data_here, cycle_length=cycle_length)\n", "\n", "# 使用掩码初始化Tigramite数据框，这里不需要missing_flag\n", "dataframe = pp.DataFrame(data_here, mask=mask, var_names=var_names, missing_flag=999.)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 因果效应分析\n", "\n", "现在的因果问题是关于因果效应估计和中介作用。这种量化需要以因果时间序列图的形式对因果关系的定性知识。因果发现，即从数据中学习因果图，对于如此短的样本量来说是具有挑战性的，因此使用领域知识来提出下面显示的定性反馈图，时间滞后大致基于Gushchina等人2020年的风速（见论文）。\n", "\n", "左侧的过程图（有时也称为摘要图）聚合了明确描述时间依赖结构的时间序列图。由于大气过程很快，假设存在同期因果效应（即在数据时间分辨率2个月以下的时间尺度上的因果影响）。然而，为了简单起见，假设没有同期因果循环（即，例如，如果WPAC$_t$对CPAC$_t$有因果影响，那么CPAC$_t$不能对WPAC$_t$有因果影响，反之亦然）。\n", "\n", "有关如何指定图的信息，请参见[因果效应教程](https://github.com/jakobrunge/tigramite/blob/master/tutorials/causal_effect_estimation/tigramite_tutorial_general_causal_effect_analysis.ipynb)。"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 600x250 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 因果效应估计\n", "# 图的形状为 (N, N, tau_max + 1)\n", "graph = np.array([\n", "        [['', '', ''],\n", "        ['<--', '', ''],\n", "        ['', '', '-->']],\n", "\n", "       [['-->', '-->', ''],\n", "        ['', '-->', ''],\n", "        ['<--', '', '']],\n", "\n", "       [['', '', ''],\n", "        ['-->', '', ''],\n", "        ['', '-->', '']]], dtype='<U3')\n", "\n", "# 过程图中节点的位置\n", "node_pos =  {\n", "            'y': np.array([0.5, 0., 0.5, 1.]),\n", "            'x': np.array([0., 0.5, 1., .5])\n", "            }\n", "\n", "# 并排显示两个图\n", "fig, axes = plt.subplots(ncols=2, nrows=1, figsize=(6, 2.5))\n", "\n", "tp.plot_graph(\n", "    fig_ax = (fig, axes[0]),\n", "    graph = graph,\n", "    node_pos=node_pos,\n", "    arrow_linewidth=5,\n", "    node_size=0.2,\n", "    node_aspect=1.5,\n", "    var_names=var_names,\n", "    tick_label_size=6,\n", "    )\n", "axes[0].set_title('Process graph', pad=20)\n", "\n", "tp.plot_time_series_graph(\n", "    fig_ax = (fig, axes[1]),\n", "    graph = graph,\n", "    var_names=var_names,\n", "    )\n", "axes[1].set_title('Time series graph', pad=20)\n", "\n", "fig.tight_layout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["人们可以争论在这里应该假设哪种图。例如，可以整合隐藏的混杂因子，即因果影响两个观察变量的未观察变量（见生物地球科学示例）。在这里，由于上述去除长期趋势和季节周期的预处理步骤，以及限制在可以假设平稳性的时间段内，人们可能合理地假设不存在隐藏的混杂。更彻底的分析可以，例如，另外考虑与海表温度的相互依赖性作为隐藏混杂的可能来源。因果推断的优势在于透明地阐述这些假设。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用这个图，因果问题首先是关于总因果效应估计，其次是关于三个变量之间的线性中介分析（有关这些主题的理论，请参见综述论文中的因果效应部分）。\n", "\n", "根据QAD流程图，这里只有一个数据集。从$X=\\text{CPAC}_{t}$对$Y=\\text{WPAC}_{t}$开始，图中沿着因果链CPAC$_t \\to $WCPAC$_t \\to $WPAC$_t$的总因果效应没有隐藏的混杂因子。此外，考虑到样本量较小，线性假设是合理的，这根据流程图建议使用路径方法。\n", "\n", "然而，谨慎的做法是也评估替代方法，这里通过使用最优调整集$\\mathbf{Z}=\\{\\text{WCPAC}_{t-1}\\}$的协变量调整（理论请参见论文中因果效应部分关于协变量调整、最优调整和线性因果效应估计以及路径方法的段落）。\n", "\n", "### 因果效应估计的（最优）调整\n", "\n", "这将使用Tigramite的``CausalEffects``类，请参见[相应的教程]()。"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X = [('CPAC', 0)] -----> Y = [('WPAC', 0)]\n", "Oset =  [('WCPAC', -1)]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 300x250 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 定义原因X和效应Y\n", "X = [(2, 0)]\n", "Y = [(0, 0)]\n", "\n", "# 使用上面定义的平稳有向无环图(DAG)初始化类\n", "causal_effects = CausalEffects(graph, graph_type='stationary_dag', \n", "                                    X=X, Y=Y, \n", "                                   S=None, # S could be a modulating variable to estimate conditional causal effects\n", "                                   verbosity=0)\n", "\n", "print(\"X = %s -----> Y = %s\" % (str([(var_names[var[0]], var[1]) for var in X]), str([(var_names[var[0]], var[1]) for var in Y])))\n", "\n", "# 获取最优调整集\n", "opt = causal_effects.get_optimal_set()\n", "\n", "if opt is False: print(\"NOT IDENTIFIABLE!\")\n", "print(\"Oset = \", [(var_names[v[0]], v[1]) for v in opt])\n", "# 可选择检查这里是否存在最优调整集\n", "# 即在所有调整集中具有最小渐近方差的意义上（见[Runge 2021](https://arxiv.org/abs/2102.10324)）\n", "# print(\"Optimality = %s\" %str(causal_effects.check_optimality()))\n", "\n", "# 为节点着色\n", "special_nodes = {}\n", "for node in opt:\n", "    special_nodes[node] = 'orange'\n", "for node in causal_effects.M:\n", "    special_nodes[node] = 'lightblue'\n", "for node in causal_effects.X:\n", "    special_nodes[node] = 'red'\n", "for node in causal_effects.Y:\n", "    special_nodes[node] = 'blue'\n", "    \n", "fig, ax = tp.plot_time_series_graph(\n", "        graph = graph,\n", "        special_nodes=special_nodes,\n", "        var_names=var_names,\n", "        figsize=(3, 2.5),\n", "        )\n", "plt.tight_layout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["中介变量以浅蓝色显示。条件集$\\mathbf{Z}$阻断了$\\text{CPAC}_{t-1}$以及进一步滞后混杂因子对$X$和$Y$的非因果混杂。\n", "\n", "我们现在使用``fit_total_effect``函数拟合和预测X对Y的总效应，并使用``fit_bootstrap_of``函数获得不确定性估计。这里将干预设置为0和1，并计算相应预测的差值，得到对应于线性回归系数的因果效应。注意，由于上面执行的异常化，数据大致是标准化的。"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total effect via adjustment = -0.31 [-0.39, -0.11]\n"]}], "source": ["# 可选的数据变换\n", "data_transform = None  # sklearn.preprocessing.StandardScaler()\n", "\n", "# 置信区间范围\n", "conf_lev = 0.9\n", "\n", "# 从观测数据拟合因果效应模型\n", "causal_effects.fit_total_effect(\n", "    dataframe=dataframe, \n", "    mask_type='y',\n", "    estimator=LinearRegression(),\n", "    data_transform=data_transform,\n", "    )\n", "\n", "# 拟合自举因果效应模型\n", "causal_effects.fit_bootstrap_of(\n", "    method='fit_total_effect',\n", "    method_args={'dataframe':dataframe,  \n", "    'mask_type':'y',\n", "    'estimator':LinearRegression(),\n", "    'data_transform':data_transform,\n", "    },\n", "    seed=4\n", "    )\n", "\n", "# 定义干预\n", "dox_vals = np.linspace(0., 1., 2)\n", "\n", "# 一次性预测干预do(X=0.), ..., do(X=1.)的效应\n", "intervention_data = np.repeat(dox_vals.reshape(len(dox_vals), 1), axis=1, repeats=len(X))\n", "pred_Y = causal_effects.predict_total_effect( \n", "        intervention_data=intervention_data)\n", "\n", "# 自举：一次性预测干预do(X=0.), ..., do(X=1.)的效应\n", "intervention_data = np.repeat(dox_vals.reshape(len(dox_vals), 1), axis=1, repeats=len(X))\n", "conf = causal_effects.predict_bootstrap_of(\n", "    method='predict_total_effect',\n", "    method_args={'intervention_data':intervention_data},\n", "    conf_lev=conf_lev)\n", "\n", "print(\"Total effect via adjustment = %.2f [%.2f, %.2f]\"\n", "        %(pred_Y[1]-pred_Y[0], conf[0,1]-conf[0,0], conf[1,1]-conf[1,0])) \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Wright路径方法用于因果效应估计\n", "\n", "为此，由于假设没有未观察的节点，我们可以使用``CausalEffects``类中的Wright路径方法功能。我们将把该方法包括自举置信区间估计包装成一个函数。"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def get_path_effect(graph, X, Y, mediation=None, conf_lev=0.9):\n", "    causal_effects = CausalEffects(graph, graph_type='stationary_dag', \n", "                                    X=X, Y=Y, \n", "                                   S=None, \n", "                                   verbosity=0)\n", "    \n", "    dox_vals = np.linspace(0., 1., 2)\n", "\n", "    # 从观测数据拟合因果效应模型\n", "    causal_effects.fit_wright_effect(\n", "        dataframe=dataframe, \n", "        mask_type='y',\n", "        mediation=mediation, #'direct',\n", "        method = 'parents',\n", "        data_transform=data_transform, #sklearn.preprocessing.StandardScaler(),\n", "        )\n", "\n", "    # 一次性预测干预do(X=0.), ..., do(X=1.)的效应\n", "    intervention_data = np.repeat(dox_vals.reshape(len(dox_vals), 1), axis=1, repeats=len(X))\n", "    pred_Y = causal_effects.predict_wright_effect( \n", "            intervention_data=intervention_data)\n", "\n", "    # 从观测数据拟合因果效应模型\n", "    causal_effects.fit_bootstrap_of(\n", "        method='fit_wright_effect',\n", "        method_args={'dataframe':dataframe,  \n", "        'mask_type':'y',\n", "        'mediation':mediation, #'direct',\n", "        'data_transform':data_transform,\n", "        },\n", "        seed=42\n", "        )\n", "\n", "    # 一次性预测干预do(X=0.), ..., do(X=1.)的效应\n", "    intervention_data = np.repeat(dox_vals.reshape(len(dox_vals), 1), axis=1, repeats=len(X))\n", "    conf = causal_effects.predict_bootstrap_of(\n", "        method='predict_wright_effect',\n", "        method_args={'intervention_data':intervention_data},\n", "        conf_lev=conf_lev)\n", "\n", "    print(\"<PERSON>'s path effect    = %.2f [%.2f, %.2f]\"\n", "                %(pred_Y[1], conf[0,1], conf[1,1]))\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>'s path effect    = -0.24 [-0.40, -0.09]\n"]}], "source": ["get_path_effect(graph, X=X, Y=Y, mediation=None, conf_lev=0.9)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这里我们通过路径方法得到了类似的总效应。注意在这种情况下路径方法的置信区间更大。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 线性中介分析\n", "\n", "最后，假设没有潜在混杂因子的线性中介分析（理论请参见论文中因果效应部分的线性中介分析段落）也可以通过路径方法进行。$X=\\text{WCPAC}_{t-1}$通过$M=\\text{WCPAC}_{t}$对$Y=\\text{WPAC}_{t}$的线性中介效应（见图）是："]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>'s path effect    = 0.33 [0.10, 0.50]\n"]}], "source": ["get_path_effect(graph, X=[(1, -1)], Y=[(0, 0)], mediation=[(1, 0)], conf_lev=0.9)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["另一方面，不通过$M$中介的直接效应是："]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>'s path effect    = 0.31 [0.12, 0.52]\n"]}], "source": ["get_path_effect(graph, X=[(1, -1)], Y=[(0, 0)], mediation='direct', conf_lev=0.9)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["中介效应和非中介效应加起来等于总因果效应："]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>'s path effect    = 0.64 [0.52, 0.79]\n"]}], "source": ["get_path_effect(graph, X=[(1, -1)], Y=[(0, 0)], mediation=None, conf_lev=0.9)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["所有这些效应都以季节标准化数据的单位表示。\n", "\n", "需要注意的是，由于这里没有隐藏的混杂，您也可以使用（通常）更快的``LinearMediation``类进行线性因果效应和中介估计（见[线性中介教程](https://github.com/jakobrunge/tigramite/blob/master/tutorials/causal_effect_estimation/tigramite_tutorial_linear_causal_effects_mediation.ipynb)）。为此，您需要为该类提供所有节点的父节点（从图中读取）："]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0: [(1, 0), (1, -1)], 1: [(2, 0), (1, -1)], 2: [(2, -1), (0, -2)]}\n"]}], "source": ["parents = {}\n", "for j in range(causal_effects.N):\n", "    parents[j] = list(causal_effects._get_parents((j, 0)))\n", "print(parents)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["然后我们可以初始化``LinearMediation``类并拟合链接系数，包括它们不确定性的自举估计。"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["med = LinearMediation(dataframe=dataframe,\n", "                 data_transform=data_transform,\n", "                 mask_type='y',)\n", "med.fit_model(all_parents=parents, tau_max=graph.shape[2] - 1)\n", "med.fit_model_bootstrap( \n", "            boot_blocklength=1,\n", "            seed=4,\n", "            boot_samples=200)\n", "\n", "def get_linear_mediation_effect(i, j, tau, mediation=None, conf_lev=0.9):\n", "    ce = med.get_ce(i=i, tau=tau,  j=j)\n", "    ce_boots = med.get_bootstrap_of(function='get_ce', \n", "        function_args={'i':i, 'tau':tau,  'j':j}, conf_lev=0.9)\n", "    # 获取因果效应和90%置信区间\n", "    print(f\"Effect of {i} on {j} with mediation = {mediation} is {ce:.2f} [{ce_boots[0]:.2f}, {ce_boots[1]:.2f}]\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Effect of 2 on 0 with mediation = None is -0.24 [-0.49, -0.07]\n", "Effect of 2 on 0 with mediation = 1 is -0.24 [-0.49, -0.07]\n", "Effect of 1 on 0 with mediation = None is 0.64 [0.50, 0.77]\n"]}], "source": ["get_linear_mediation_effect(i=2, j=0, tau=0, mediation=None, conf_lev=0.9)\n", "get_linear_mediation_effect(i=2, j=0, tau=0, mediation=1, conf_lev=0.9)\n", "\n", "get_linear_mediation_effect(i=1, j=0, tau=1, mediation=None, conf_lev=0.9)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["注意，与``CausalEffects``不同，``LinearMediation``类在过程级别上工作，中介的计算方式不同：``mediation=1``计算通过变量1在*任何*滞后的所有路径。这里不允许区分通过变量1的同期节点的中介。``CausalEffects``类允许在不同滞后指定中介。\n", "\n", "``LinearMediation``类提供了丰富的进一步功能。请查看教程。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这个例子演示了如何在因果推断分析中利用领域知识（如变量定义和假设的定性图）结合预处理步骤来确保某些假设，以量化因果效应。\n", "\n", "有许多方法可以修改这种分析的设置：从选择不同的变量，到考虑海表温度的混杂，再到不同的预处理步骤和关于图的替代假设。更全面的分析可以并且会透明地报告这些不同假设如何改变结论。\n", "\n", "请查看关于tigramite各种方法和功能的其他教程。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tigenv", "language": "python", "name": "tigenv"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 4}